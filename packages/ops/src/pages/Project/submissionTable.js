import React from 'react';
import { get } from 'lodash';
import { Link, useParams } from 'react-router-dom';
import {
  List,
  Datagrid,
  FunctionField,
  // useRedirect,
  TopToolbar,
  TextField,
} from 'react-admin';
import InboxIcon from '@mui/icons-material/Inbox';
import { Stack, Box, Typography } from '@mui/material';
import { getSubmissionStatusBadge } from '../../helpers/helper';
import Badge from '../../sharedComponents/badge';

const SubmissionList = ({ projectCreatorId }) => {
  const { id } = useParams();
  // Debug logs for filter values
  React.useEffect(() => {
    console.log('SubmissionList debug:', { projectCreatorId, projectId: id });
  }, [projectCreatorId, id]);
  // const redirect = useRedirect();

  const CustomToolbar = () => (
    <TopToolbar sx={{ minHeight: '10px !important' }} />
  );

  return (
    <Box sx={{ mt: 4, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}>
      <Stack
        direction="row"
        spacing={2}
        sx={{ justifyContent: 'space-between' }}
      >
        <Typography variant="subtitle1" fontWeight="bold">
          Submissions
        </Typography>
      </Stack>
      <List
        resource="submissionList"
        exporter={false}
        filter={{ projectId: id }}
        disableSyncWithLocation
        title={false}
        empty={
          <>
            <Box textAlign="center" p={2}>
              <InboxIcon sx={{ fontSize: 100 }} />
              <Typography variant="h6" color="textSecondary">
                No Submissions Yet
              </Typography>
            </Box>
          </>
        }
        actions={<CustomToolbar />}
      >
        <Datagrid bulkActionButtons={false}>
          <FunctionField
            source="snapshotName"
            label={<span style={{ fontWeight: 'bold' }}>Snapshot name</span>}
            render={(record) => {
              console.log('Submission record:', record);
              return (
                <Link
                  className="admin-link"
                  target="_blank"
                  to={`${process.env.REACT_APP_WEBAPP_URL}/project/snap/${get(
                    record,
                    'snapshotHash',
                    ''
                  )}`}
                >
                  {get(record, 'snapshotName', 'Untitled Snapshot')}
                </Link>
              );
            }}
          />

          <FunctionField
            source="calloutName"
            label={<span style={{ fontWeight: 'bold' }}>Callout name</span>}
            render={(record) => (
              <Link
                className="admin-link"
                to={`/callouts/${get(record, 'calloutId', '')}/show`}
              >
                {get(record, 'calloutName', 'Unknown Callout')}
              </Link>
            )}
          />

          <TextField
            source="addedAt"
            label={
              <span
                style={{
                  fontWeight: 'bold',
                  fontSize: '14px',
                  color: 'black',
                }}
              >
                Date submitted
              </span>
            }
          />
          <FunctionField
            source="status"
            sortBy="submissionsStatus"
            label={<span style={{ fontWeight: 'bold' }}>Status</span>}
            render={(record) => {
              const subsStatus = get(record, 'status', 'NEW');

              const submittedByRole = get(record, 'submittyedByRole', '');
              const { badgeColor, labelText } =
                getSubmissionStatusBadge(subsStatus);

              // Cupid Submission logic
              let finalLabelText = labelText;
              let finalBadgeColor = badgeColor;
              if (submittedByRole === 'cupid' && subsStatus === 'NEW') {
                finalLabelText = 'Cupid Submission';
                finalBadgeColor = 'badge-success';
              }

              return (
                <Badge
                  list={[{ label: finalLabelText, className: finalBadgeColor }]}
                />
              );
            }}
          />
        </Datagrid>
      </List>
    </Box>
  );
};

export default SubmissionList;
