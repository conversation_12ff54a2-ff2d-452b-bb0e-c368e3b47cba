import React, { useState } from 'react';
import {
  List,
  Datagrid,
  TextField,
  useDataProvider,
  useNotify,
  useRefresh,
  Confirm,
  FunctionField,
  useGetList,
  useGetOne,
  TopToolbar,
} from 'react-admin';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { get, truncate } from 'lodash';
import { Box, Stack, Typography } from '@mui/material';
import InboxIcon from '@mui/icons-material/Inbox';
import BasicModal from '../../sharedComponents/modal';
import { ProjectSnapshotActions } from '../../helpers/helper';
import Badge from '../../sharedComponents/badge';

const SnapShotList = (props) => {
  const [isOpenModal, setOpenModal] = useState(false);
  const [isAddToSlateModalOpen, setIsAddToSlateModalModal] = useState(false);
  const [snapshotData, setSnapshotData] = useState(null);
  const [callOutData, setCallOutData] = useState(null);
  const [reset, setReset] = useState(false);
  const [callOutList, setCallOutList] = useState([]);
  const [triggerFetch, setTriggerFetch] = useState(false);
  const dataProvider = useDataProvider();
  const notify = useNotify();
  const refresh = useRefresh();
  const { id } = useParams();

  useGetList(
    'callouts',
    {
      pagination: { page: 1, perPage: 100000 },
      sort: { field: 'createdAt', order: 'DESC' },
      meta: { select: ['id', 'name', 'discoverer.name'] },
    },
    {
      enabled: triggerFetch,
      onSuccess: (resp) => {
        setTriggerFetch(false);
        setOpenModal(true);
        setReset(!reset);
        const { data } = resp;
        setCallOutList(
          data
            ? data.map((callout) => {
                return {
                  key: callout.id,
                  label:
                    get(callout, 'discoverer', 'undefined') +
                    ' - ' +
                    get(callout, 'name', 'undefined'),
                };
              })
            : []
        );
      },
      onError: (error) => {
        notify(`${String(error)}`, {
          type: 'error',
          multiLine: true,
          autoHideDuration: 500,
        });
        setTriggerFetch(false);
        setOpenModal(true);
        setReset(!reset);
      },
    }
  );

  const { data, isLoading } = useGetOne('projects', { id });

  if (isLoading) return <div>Loading...</div>;
  if (!data) return <div>No data found</div>;

  const handleAddToSlateSubmit = () => {
    dataProvider
      .create('v1/submission', {
        data: {
          calloutId: callOutData.key,
          snapshotId: snapshotData.id,
        },
      })
      .then(() => {
        notify('Snapshot submitted successfully!', { type: 'success' });
        setSnapshotData(null);
        setCallOutData(null);
        setIsAddToSlateModalModal(false);
        refresh();
      })
      .catch((error) => {
        notify(error.message, { type: 'error' });
        setIsAddToSlateModalModal(false);
      });
  };

  const clickHandler = (val, action) => {
    switch (action) {
      case 'addToCallout': {
        setSnapshotData(val);
        setTriggerFetch(true);
        break;
      }
      default: {
        window.open(
          `${process.env.REACT_APP_WEBAPP_URL}/project/snap/${get(
            val,
            'hash'
          )}`,
          '_blank'
        );
        break;
      }
    }
  };

  const CustomToolbar = () => (
    <TopToolbar sx={{ minHeight: '10px !important' }} />
  );

  return (
    <Box sx={{ mt: 4, p: 2, border: '1px solid #ddd', borderRadius: '5px' }}>
      <Stack
        direction="row"
        spacing={2}
        sx={{ justifyContent: 'space-between' }}
      >
        <Typography variant="subtitle1" fontWeight="bold">
          Project snapshots
        </Typography>
      </Stack>
      <List
        resource="snapshots"
        filter={{
          id: data.id,
        }}
        exporter={false}
        disableSyncWithLocation
        title={false}
        empty={
          <>
            <Box textAlign="center" p={2}>
              <InboxIcon sx={{ fontSize: 100 }} />
              <Typography variant="h6" color="textSecondary">
                No Snapshot Yet
              </Typography>
            </Box>
          </>
        }
        actions={<CustomToolbar />}
      >
        <div className="mt-2 px-3">
          <Datagrid bulkActionButtons={false}>
            <FunctionField
              source="snapshotTitle"
              sortable={false}
              label={<span style={{ fontWeight: 'bold' }}>Project Name</span>}
              render={(record) => (
                <Link
                  className="admin-link"
                  target="_blank"
                  to={`${process.env.REACT_APP_WEBAPP_URL}/project/snap/${record.hash}`}
                >
                  {record.snapshotTitle}
                </Link>
              )}
            />
            <TextField
              source="created"
              sortable={false}
              label={
                <span
                  style={{
                    fontWeight: 'bold',
                    fontSize: '14px',
                    color: 'black',
                  }}
                >
                  Created
                </span>
              }
            />
            <TextField
              source="updated"
              sortable={false}
              label={
                <span
                  style={{
                    fontWeight: 'bold',
                    fontSize: '14px',
                    color: 'black',
                  }}
                >
                  Updated
                </span>
              }
            />
            <FunctionField
              source="snapShotStatus"
              sortable={false}
              label={<span style={{ fontWeight: 'bold' }}>Status</span>}
              render={(record) => {
                const subsStatus = get(record, 'snapShotStatus');
                return (
                  <Badge
                    list={[{ label: subsStatus, className: 'admin-btn' }]}
                  />
                );
              }}
            />
            <FunctionField
              source="projectSnapshotAction"
              sortable={false}
              label={
                <span
                  style={{
                    fontWeight: 'bold',
                    fontSize: '14px',
                    color: 'black',
                  }}
                >
                  Actions
                </span>
              }
              render={(record) => (
                <ProjectSnapshotActions
                  value={record}
                  header={{ clickHandler: clickHandler }}
                />
              )}
            />
          </Datagrid>
        </div>
        <BasicModal
          title="Add to call out submissions"
          content=""
          open={isOpenModal}
          list={callOutList}
          onClose={() => setOpenModal(false)}
          onSuccess={() => setIsAddToSlateModalModal(true)}
          clickHandler={(item) => {
            setCallOutData(item);
            setIsAddToSlateModalModal(true);
            setOpenModal(false);
          }}
          placeHolder="Search call outs"
        />

        <Confirm
          loading={false}
          isOpen={isAddToSlateModalOpen}
          title={`Add ${truncate(
            get(snapshotData, 'snapshotTitle', 'Unknown'),
            {
              length: 30,
            }
          )} snapshot to call out submission`}
          content="Are you certain you want to submit this snapshot in the call out submission?"
          onConfirm={handleAddToSlateSubmit}
          onClose={() => {
            setIsAddToSlateModalModal(false);
            setOpenModal(true);
          }}
        />
      </List>
    </Box>
  );
};

export default SnapShotList;
