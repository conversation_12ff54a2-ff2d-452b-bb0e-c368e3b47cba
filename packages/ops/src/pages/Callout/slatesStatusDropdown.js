import { useState } from 'react';
import { Select, MenuItem, FormControl, Chip } from '@mui/material';

const statusOptions = [
  {
    label: 'Awaiting feedback',
    value: 'AWAITING_FEEDBACK',
    color: '#EAEAEA',
    textColor: '#000',
  },
  {
    label: 'Let’s talk',
    value: 'LETS_TALK',
    color: '#3DDC97',
    textColor: '#000',
  },
  { label: 'Tracking', value: 'TRACKING', color: '#FFD700', textColor: '#000' },
  {
    label: 'Not for me',
    value: 'NOT_INTERESTED',
    color: '#E53935',
    textColor: '#fff',
  },
];

const StatusField = ({ record, onStatusChange }) => {
  const [status, setStatus] = useState(record?.status || 'AWAITING_FEEDBACK');
  // const notify = useNotify();
  // const dataProvider = useDataProvider();
  // const refresh = useRefresh(); // To refresh the UI

  const handleChange = async (event) => {
    const newStatus = event.target.value;
    setStatus(newStatus);

    if (onStatusChange) {
      onStatusChange(newStatus, record);
    }
  };

  return (
    <FormControl variant="outlined" size="small">
      <Select
        value={status}
        onChange={handleChange}
        sx={{
          backgroundColor: statusOptions.find((opt) => opt.value === status)
            ?.color,
          color: statusOptions.find((opt) => opt.value === status)?.textColor,
          fontWeight: 'bold',
          borderRadius: '20px',
          minWidth: 150,
        }}
      >
        {statusOptions.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            <Chip
              label={option.label}
              sx={{
                backgroundColor: option.color,
                color: option.textColor,
                fontWeight: 'bold',
              }}
            />
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default StatusField;
