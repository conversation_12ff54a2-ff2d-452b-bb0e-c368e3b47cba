import moment from 'moment';
import { useListContext, Pagination } from 'react-admin';
import _, { replace } from 'lodash';
import classNames from 'classnames';
import options from '../configurations/option.json';
import { Stack } from '@mui/material';
import HoverToolTip from '../sharedComponents/ToolTip/hoverToolTip';
import InlineSvg from '../sharedComponents/inline-svg/index';
export const rowStyle = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: '16px 0',
  width: '800px',
};

export const PostPagination = () => {
  const { total } = useListContext();

  const rowsPerPageOptions = [
    5,
    10,
    25,
    50,
    { label: 'All', value: total > 0 ? total : 100000 },
  ];

  return <Pagination rowsPerPageOptions={rowsPerPageOptions} />;
};

export const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Find organisation size using the organisation value
 *
 * @param {Event} event - The event object.
 */
export const calculateorgSize = (orgSize) => {
  const orgSizevalue = options.orgSize.find(
    (option) =>
      option.value.min === orgSize.min && option.value.max === orgSize.max
  );
  return orgSizevalue ? orgSizevalue.key : '';
};

export const replaceHtmlTags = (htmlString, tag) => {
  let updatedString = htmlString.replace(/<p>/g, `<${tag}>`);
  updatedString = updatedString.replace(/<\/p>/g, `</${tag}>`);
  return updatedString;
};

/**
 * Compose discoverer view data
 *
 * @param {*} roots - root elements
 * @param {*} data - data object
 * @returns {*} - Composed discoverer view data
 */
export const sanitizeEditorData = (roots, data) => {
  const initialData = {};
  for (const key in data) {
    if (data[key] && roots.includes(key)) {
      initialData[key] = data[key];
    }
  }
  return initialData;
};

export const mergeCls = (...classes) => {
  return classNames(...classes);
};

export const removeHtmlTags = (html) => {
  var doc = new DOMParser().parseFromString(html, 'text/html');
  return doc.body.textContent || '';
};

export const formatDate = (timestamp) => {
  try {
    if (!timestamp) throw new Error('Invalid timestamp');
    return new Intl.DateTimeFormat('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
    })
      .format(new Date(timestamp))
      .replace(',', '');
  } catch (error) {
    console.error('Error formatting date:', error.message);
    return 'Invalid date';
  }
};

/**
 * Converts user data to a structured object for display in a table row.
 *
 * @param {Object} item - User data object.
 * @returns {Object} - Formatted user row data.
 */

/**
 * Converts search data to query parameters for backend searching.
 *
 * @param {*} fields - fields to search
 * @param {*} q - search query
 * @returns {*} - search query
 */
export const handleSorting = (field, order) => {
  const orderType = order === 'DESC' ? -1 : 1;
  switch (field) {
    case 'id':
      return `_id|${orderType}`;
    case 'userName':
      return `profile.name.fullName|${orderType}`;
    case 'registrationNo':
      return `regNo|${orderType}`;
    case 'creatorEmail':
      return `creator.email|${orderType}`;
    case 'creatorName':
      return `creator.username|${orderType}`;
    case 'snapProjectName':
      return `projectName|${orderType}`;
    case 'snapshotName':
      return `submissions.snapshot.title|${orderType}`;
    case 'created':
      return `createdAt|${orderType}`;
    case 'updated':
      return `updatedAt|${orderType}`;
    case 'snapshotCreateAt':
      return `submissions.addedAt|${orderType}`;
    case 'submissionsStatus':
      return `submissions.status|${orderType}`;
    case 'submissionStatus':
      return `status|${orderType}`;
    case 'subscription':
      return `userMeta.type|${orderType}`;
    case 'city':
      return `profile.city.address|${orderType}`;
    case 'projectName':
      return `cover.title|${orderType}`;
    case 'projectCreator':
      return `creator.username|${orderType}`;
    case 'status':
      return `basicInfo.status|${orderType}`;
    case 'producer':
      return `cover.producer|${orderType}`;
    case 'director':
      return `cover.director|${orderType}`;
    case 'writer':
      return `cover.writer|${orderType}`;
    case 'format':
      return `cover.format|${orderType}`;
    case 'totalBudget':
      return `totalBudget|${orderType}`;
    case 'totalFunding':
      return `totalFinanceSum|${orderType}`;
    case 'projectCreatorEmail':
      return `creator.email|${orderType}`;
    case 'callOutName':
      return `name|${orderType}`;
    case 'companyName':
      return `body.companyName|${orderType}`;
    case 'discovererName':
      return `discoverer.name|${orderType}`;
    case 'submission':
      return `totalSubmissions|${orderType}`;
    case 'slate':
      return `totalSlates|${orderType}`;
    case 'organisationName':
      return `profile.organisation|${orderType}`;
    case 'organisationType':
      return `profile.organisationType|${orderType}`;
    case 'organisationSize':
      return `profile.organisationSize|${orderType}`;
    default:
      return `${field}|${orderType}`;
  }
};

export const getStatusBackgroundColor = (statusValue) => {
  const colorMap = {
    cupid_selected: '#00e5d5',
    AWAITING_FEEDBACK: '#ecece0',
    FEEDBACK_SENT: '#ebff29',
    REJECTED: '#ff303d',
    legacy: '#5a5a5a',
    free: '#ECECE0',
    enterprise: '#00e5d5',
    pro: '#05012d',
    private: '#ff303d',
    public: '#00e5d5',
    trial: '#05012d',
    NEW: '#00e5d5',
  };

  return colorMap[statusValue] || 'inherit';
};

export const getStatusTextColor = (statusValue) => {
  const colorMap = {
    AWAITING_FEEDBACK: '#212529',
    FEEDBACK_SENT: '#212529',
    REJECTED: '#ffffff',
    legacy: '#ffffff',
    free: '#212529',
    enterprise: '#212529',
    pro: '#ffffff',
    public: '#212529',
    private: '#ffffff',
    trial: '#ffffff',
  };

  return colorMap[statusValue] || 'inherit';
};

/**
 * Converts filters data to query parameters for backend filtering.
 *
 * @param {Object} filters - Filter data object.
 * @returns {Object} - Query parameters for backend filtering.
 */
export const handleFilters = (filters) => {
  const queryParameters = {};
  let fieldValueArray = [];

  for (const [key, value] of Object.entries(filters)) {
    switch (key) {
      case 'userName': {
        fieldValueArray.push(`profile.name.fullName|${value}`);
        break;
      }
      case 'user_subscription_type': {
        fieldValueArray.push(`userMeta.type|${value}`);
        break;
      }
      case 'project_subscription_type': {
        fieldValueArray.push(`creator.metadata.subscriptionType|${value}`);
        break;
      }
      case 'createdAtBefore': {
        queryParameters['createdAt[$lte]'] = moment(value)
          .endOf('day')
          .toISOString();
        break;
      }
      case 'createdAtAfter': {
        queryParameters['createdAt[$gte]'] = moment(value)
          .startOf('day')
          .toISOString();
        break;
      }
      case 'updatedAtBefore': {
        queryParameters['updatedAt[$lte]'] = moment(value)
          .endOf('day')
          .toISOString();
        break;
      }
      case 'updatedAtAfter': {
        queryParameters['updatedAt[$gte]'] = moment(value)
          .startOf('day')
          .toISOString();
        break;
      }
      case 'basicInfo_status': {
        queryParameters['basicInfo.status'] = value;
        break;
      }
      case 'basicInfo_format': {
        queryParameters['basicInfo.format'] = value;
        break;
      }
      case 'basicInfo_genre': {
        queryParameters['basicInfo.genre'] = value;
        break;
      }
      case 'basicInfo_setting': {
        queryParameters['basicInfo.setting'] = value;
        break;
      }
      case 'treatment': {
        queryParameters['treatmentFilter'] = value;
        break;
      }
      case 'script': {
        queryParameters['scriptFilter'] = value;
        break;
      }
      case 'basicInfo_logLine': {
        queryParameters['logLineFilter'] = value;
        break;
      }
      case 'snapshot': {
        queryParameters['snapshotFilter'] = value;
        break;
      }
      default: {
        const field = replace(key, /_/g, '.');
        fieldValueArray.push(`${field}|${value}`);
        break;
      }
    }
  }
  if (fieldValueArray.length > 0) {
    queryParameters.$isearch = fieldValueArray.join(',');
  }

  if (
    queryParameters['createdAt[$lte]'] &&
    queryParameters['createdAt[$gte]']
  ) {
    const lte = queryParameters['createdAt[$lte]'];
    const gte = queryParameters['createdAt[$gte]'];
    queryParameters.createdAtFilter = JSON.stringify({
      $lte: lte,
      $gte: gte,
    });
    delete queryParameters['createdAt[$lte]'];
    delete queryParameters['createdAt[$gte]'];
  }

  if (
    queryParameters['updatedAt[$lte]'] &&
    queryParameters['updatedAt[$gte]']
  ) {
    const lte = queryParameters['updatedAt[$lte]'];
    const gte = queryParameters['updatedAt[$gte]'];
    queryParameters.updatedAtFilter = JSON.stringify({
      $lte: lte,
      $gte: gte,
    });
    delete queryParameters['updatedAt[$lte]'];
    delete queryParameters['updatedAt[$gte]'];
  }

  return queryParameters;
};

/**
 * Converts search data to query parameters for backend searching.
 *
 * @param {*} fields - fields to search
 * @param {*} q - search query
 * @returns {*} - search query
 */
export const handleSearchQuery = (text, fields) => {
  const searchQuery = {};
  if (text && fields.length > 0) {
    searchQuery.$q = `${text}|${fields.join(',')}`;
  }
  return searchQuery;
};

/*****to check organsation size *************/
export const organisationCount = (size) => {
  if (_.isString(size)) {
    try {
      size = JSON.parse(size);
    } catch {
      return null;
    }
  }

  return size?.min === 1 && size.max === 1
    ? '1 Employee'
    : size?.min >= 50 && size.max >= 1000000
      ? '50+ Employees'
      : size?.min > 0 && size.max < 50
        ? `${size.min}-${size.max} Employees`
        : size?.min > 0
          ? `${size.min}+ Employees`
          : null;
};

/**
 * Converts select data to query parameters for backend filtering.
 *
 * @param {*} select - fields to select
 * @returns {*} - select query
 */
export const handleSelect = (select) => {
  if (!select) return null;
  const selectQuery = {};
  if (select.length > 0) {
    selectQuery.$select = select.join(' ');
  }
  return selectQuery;
};

export const ProjectSnapshotActions = ({ value, header }) => (
  <Stack direction="row" spacing={{ xs: 1, sm: 2, md: 2 }}>
    <HoverToolTip
      key={'viewSnapshot-btn'}
      clickIcon={
        <InlineSvg
          src="/assets/svg/view-stroke.svg"
          onClick={() => header.clickHandler(value, 'viewSnapshot')}
          height={20}
          width={20}
        />
      }
      position="top"
      contentClass="p2 text-left"
      content="View Snapshot"
    />
    <HoverToolTip
      key={'addToCallout-btn'}
      clickIcon={
        <InlineSvg
          src="/assets/svg/add-circle-stroke-rounded.svg"
          onClick={() => header.clickHandler(value, 'addToCallout')}
          height={20}
          width={20}
        />
      }
      position="top"
      contentClass="p2 text-left"
      content="Submit to a call out"
    />
  </Stack>
);

export const getSubmissionStatusBadge = (value) => {
  const statusMap = {
    NEW: { badgeColor: 'badge-success', labelText: 'New' },
    FEEDBACK_SENT: { badgeColor: 'badge-warning', labelText: 'Feedback sent' },
    REJECTED: { badgeColor: 'badge-danger', labelText: 'Rejected' },
    TRACKING: { badgeColor: 'badge-warning', labelText: 'Tracking' },
    NOT_INTERESTED: { badgeColor: 'badge-danger', labelText: 'Not for me' },
    LETS_TALK: { badgeColor: 'badge-success', labelText: 'Let’s talk' },
    AWAITING_FEEDBACK: {
      badgeColor: 'badge-info ',
      labelText: 'Awaiting feedback',
    },
  };

  return (
    statusMap[value] || { badgeColor: 'badge-secondary', labelText: value }
  );
};
