.modalTitle {
  text-align: center;
  font-size: 20px;
  line-height: 24px;
  color: #05012d;
  margin-bottom: 0px !important;
}

.inviteModal > div > div {
  background-color: #ecece0;
  padding: 40px;
}

.inviteInputHeading {
  font-family: 'maisonNeue';
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
  color: #05012d;
}

.collaboratorRow {
  padding: 12px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;

  &:last-child {
    border-bottom: none;
  }
}

.collaboratorAvatar {
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  margin-right: 12px; // ✅ Gap between avatar and email

  img {
    object-fit: cover;
    width: 100%;
    height: 100%;
    border-radius: 50%;
  }
}

.collaboratorInfo {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  font-size: 14px;
}

.collaboratorEmail,
.collaboratorName {
  font-family: 'maisonNeue';
  font-size: 14px;
  color: #05012d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.collaboratorEmail {
  opacity: 0.6;
}

.collaboratorName {
  font-weight: 500;
}

.invitedBadge {
  font-family: 'maisonNeue';
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 4px 10px;
  background-color: #ffffff;
  color: #05012d;
  border: 1px solid #e5e5e5;
  white-space: nowrap;
  line-height: 150%;
}

// Reuse same base style for both
.pendingBadge {
  @extend .invitedBadge;
}

.acceptedBadge {
  @extend .invitedBadge;
}

.collaboratorActions {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  justify-content: flex-end;
}

.actionLink {
  font-family: 'maisonNeue';
  font-size: 13px;
  color: #1743d7;
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;

  &:hover {
    text-decoration: underline;
  }
}

.inviteBtnText {
  font-family: 'chaney';
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 24px;
  color: #ffffff;
  background-color: #05012d;
  border: none;
  border-radius: 0; // 🟩 Flat rectangle fix
  width: 100%;
  padding: 8px 28px;
  height: 48px;

  &:hover,
  &:focus {
    background-color: #05012d;
    color: #ffffff;
    box-shadow: none;
  }
}

.inviteFormRow {
  margin-bottom: 28px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-end;
}

.invite-button-wrapper {
  flex-shrink: 0;
  width: 160px;

  @media (max-width: 576px) {
    width: 100%;
  }
}

// Optional (to remove unused hover logic from before)
.pendingText {
  display: none !important;
}

.pendingName:hover .pendingText {
  display: none !important;
}

.modalHeader {
  margin-bottom: 0px !important;
}

.svgIconClass {
  width: 24px;
  height: 24px;
  margin-top: 36px;
  margin-right: 20px;
  @media (max-width: 576px) {
    margin-top: 16px !important;
    margin-right: 7px !important;
  }
}

.modalFooter {
  display: none !important;
}

.footerRightAlign {
  justify-content: flex-end !important;
}

.bodyClass {
  font-family: 'maisonNeue';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 150%;
}
