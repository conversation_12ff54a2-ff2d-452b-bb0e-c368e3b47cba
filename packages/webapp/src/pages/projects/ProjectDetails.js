// ProjectDetails.js
import React from 'react';
import ProjectList from './components/ProjectList';
import SectionHeader from 'sharedComponents/SectionHeader';
import Button from 'sharedComponents/Button/button';
import Router from 'next/router';

const ProjectDetails = ({
  myProjects,
  collaborations,
  accessibleFeature,
  subscriptionStatus,
  collaboratorList,
  canCreateProject,
  isProjectLocked,
  isCollaborationLocked,
  setSubscriptionJourneyModal,
}) => (
  <div className="container-fluid">
    <div className="my-32 my-md-48 my-lg-48">
      {/* MY PROJECTS SECTION */}
      <div>
        <SectionHeader
          title="MY PROJECTS"
          description="Manage and create projects."
        />
        <ProjectList
          projects={myProjects}
          collaborators={collaborations}
          type="my"
          setSubscriptionJourneyModal={setSubscriptionJourneyModal}
          canCreateProject={canCreateProject}
          accessibleFeature={accessibleFeature}
          subscriptionStatus={subscriptionStatus}
          collaboratorList={collaboratorList}
          isProjectLocked={isProjectLocked}
          isCollaborationLocked={isCollaborationLocked}
        />
        <div className="row justify-content-center d-flex px-3 px-md-0 px-lg-0">
          <Button
            className="secondory-button col-12 col-md-3 text-nowrap fs-16 "
            customClass="cancelButton"
            clickHandler={() => Router.push('/deletedProjects')}
            buttonValue={'VIEW RECENTLY DELETED'}
            btntype="button"
            data-cy={'View Recently Deleted'}
          />
        </div>
      </div>
      {/* COLLABORATIONS SECTION */}
      {Array.isArray(collaborations) && collaborations.length > 0 && (
        <div className="mt-32">
          <SectionHeader
            title="COLLABORATIONS"
            description="Other peoples projects you're collaborating on"
          />
          <ProjectList
            canCreateProject={canCreateProject}
            collaboratorList={collaboratorList}
            projects={collaborations}
            type="collaboration"
            accessibleFeature={accessibleFeature}
            subscriptionStatus={subscriptionStatus}
            isProjectLocked={isProjectLocked}
            isCollaborationLocked={isCollaborationLocked}
          />
        </div>
      )}
    </div>
  </div>
);

export default ProjectDetails;
