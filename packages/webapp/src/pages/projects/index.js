import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import Head from 'next/head';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Loader from 'sharedComponents/loader';
import { withRouter } from 'next/router';
import { getUserData } from 'reducer/user';
import { getProjectList } from 'reducer/project';
import Style from './styles/startApp.module.scss';
import NewSidebar from 'sharedComponents/NewSidebar/newsidebar';
import ProjectDetails from './ProjectDetails';
import { setSubscriptionJourneyModal } from 'reducer/subscription';
import { withSubscriptionBanner } from 'sharedComponents/Subscription';
import Modal from 'sharedComponents/Modal/modal';
import { get } from 'lodash';
import {
  setCollaboratorInfoModalStatus,
} from 'reducer/project';
import style from '../project/overview/style/projectDashboard.module.scss';

class Index extends PureComponent {
  constructor(props) {
    super(props);
    this.router = null;
    this.state = {
      myProjects: [],
      collaborations: [],
    };
  }

  async componentDidMount() {
    const { token, getProjectList, getUserData } = this.props;

    if (token) {
      try {
        await getUserData();
        await getProjectList({ params: { deleted: false } });
      } catch (error) {
        console.error('Error fetching user or projects:', error);
      }
    }
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    const { userData, projectsList } = nextProps;
    const userId = userData?._id;
    const userEmail = userData?.email;

    // Single loop to categorize projects
    const myProjects = [];
    const collaborations = [];
    const safeProjectsList = Array.isArray(projectsList) ? projectsList : [];

    safeProjectsList.forEach((project) => {
      const projectData = {
        _id: project._id,
        cover: project.cover,
        creator: project.creator,
        projectCollaborator: project.projectCollaborator,
      };

      if (project.creator?.userId === userId) {
        // This is user's own project
        myProjects.push(projectData);
      } else if (
        Array.isArray(project.projectCollaborator) &&
        project.projectCollaborator.some((c) => c.email === userEmail)
      ) {
        // This is a collaboration project
        collaborations.push(projectData);
      }
    });

    // Check if state needs to be updated
    const shouldUpdateMyProjects =
      JSON.stringify(myProjects) !== JSON.stringify(prevState.myProjects);
    const shouldUpdateCollaborations =
      JSON.stringify(collaborations) !==
      JSON.stringify(prevState.collaborations);

    if (shouldUpdateMyProjects || shouldUpdateCollaborations) {
      return {
        myProjects,
        collaborations,
      };
    }

    return null;
  }

  body = () => {
    const { userData } = this.props;
    const firstName = get(userData, 'profile.name.firstName', '');
    const lastName = get(userData, 'profile.name.lastName', '');
    const fullName = `${firstName} ${lastName}`;
    return (
      <div>
        <div className="text-left">
          <p className="text-primary p2">
            You have been invited to collaborate on {fullName}
            {`'s`} project. You can go back to this project at any time from your “My Projects” page. Click continue to start collaborating now. project. You can go back to this project at any time from
            your 'My Projects' page. Click continue to start collaborating now.
          </p>
        </div>
      </div>
    );
  };



  render() {
    const {
      userData,
      isLoading,
      accessibleFeature,
      subscriptionStatus,
      collaboratorList,
      bannerWrapper: BannerWrapper,
      bannerProps,
      setCollaboratorInfoModalStatus,
      collaboratorInfoModalStatus,
      setSubscriptionJourneyModal
    } = this.props;
    const { myProjects, collaborations } = this.state;

    if (isLoading) return <Loader />;

    // Centralized logic for project creation eligibility
    const canCreateProject =
      (subscriptionStatus !== 'expired' &&
        accessibleFeature?.unlimitedProjects) ||
      (Array.isArray(myProjects) && myProjects.length < 2);

    // Centralized logic for project lock (for index > 1)
    const isProjectLocked = (idx) =>
      (idx > 1 && !accessibleFeature.unlimitedProjects) ||
      (idx > 1 && subscriptionStatus === 'expired');

    const isCollaborationLocked =
      !accessibleFeature.projectCollaborators ||
      subscriptionStatus === 'expired';


    const handleCollaboration = () => {
      if (isCollaborationLocked) {
        setSubscriptionJourneyModal({
          modalStatus: true,
          cameFrom: 'projects',
          onComplete: () => handleCollaboration(),
          feature: 'projectCollaborators',
        });
      } else {
        setSubscriptionJourneyModal({
          modalStatus: false,
          feature: '',
          cameFrom: 'projects',
          onComplete: () => { },
        });
        setCollaboratorInfoModalStatus(false)
      }
    }

    return (
      <div className={`${Style.background_dashboard} themeContainer`}>
        <Head>
          <title>Projects | Smash</title>
          <link rel="shortcut icon" href="/favicon.ico" />
        </Head>
        <div className="container-fluid">
          <div className="row">
            <Modal
              modalShow={collaboratorInfoModalStatus}
              className={`${style.modalBody}`}
              titleClass={`${style.modalPublishTitle}`}
              title="Start collaborating"
              isCollaborationLocked={isCollaborationLocked}
              body={this.body()}
              closeCallback={() => {
                setCollaboratorInfoModalStatus(false)
              }}
              isShowCrossBtn
              closeBtnText="cancel"
              successCallback={() => handleCollaboration()}
              successBtnText="continue"
              closeBtnClass="--secondaryChaney"
              successBtnClass="--primaryNavy"
            />
            <div className="col-12 col-md-2 col-lg-2 p-0">
              <NewSidebar profile={userData.profile} />
            </div>
            <div className="page-wrapper col-12 col-md-10 col-lg-10">
              {BannerWrapper ? (
                <BannerWrapper bannerProps={bannerProps}>
                  <div
                    // className="col-12 col-md-10 col-lg-10"
                    style={{
                      backgroundColor: '#F7F7F3',
                      minHeight: '100vh',
                    }}
                  >
                    <ProjectDetails
                      myProjects={myProjects}
                      collaborations={collaborations}
                      accessibleFeature={accessibleFeature}
                      subscriptionStatus={subscriptionStatus}
                      canCreateProject={canCreateProject}
                      isProjectLocked={isProjectLocked}
                      isCollaborationLocked={isCollaborationLocked}
                      collaboratorList={collaboratorList}
                    />
                  </div>{' '}
                </BannerWrapper>
              ) : (
                <div
                  // className="col-12 col-md-10 col-lg-10"
                  style={{
                    backgroundColor: '#F7F7F3',
                    minHeight: '100vh',
                  }}
                >
                  <ProjectDetails
                    myProjects={myProjects}
                    collaborations={collaborations}
                    accessibleFeature={accessibleFeature}
                    subscriptionStatus={subscriptionStatus}
                    canCreateProject={canCreateProject}
                    isProjectLocked={isProjectLocked}
                    isCollaborationLocked={isCollaborationLocked}
                    collaboratorList={collaboratorList}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale)),
    },
  };
}

Index.defaultProps = {};
Index.propTypes = {
  token: PropTypes.string.isRequired,
  userData: PropTypes.object.isRequired,
  getUserData: PropTypes.func.isRequired,
  projectsList: PropTypes.array.isRequired,
  getProjectList: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
};

const mapStateToProps = (state) => ({
  token: state.auth.token,
  userData: state.auth.userData,
  projectsList: state.project.projectsList,
  isLoading: state.project.isLoading,
  accessibleFeature: state.subscription.accessibleFeature,
  collaboratorList: state.project.collaboratorList,
  subscriptionStatus: state.subscription.subscriptionStatus,
  collaboratorInfoModalStatus: state.project.collaboratorInfoModalStatus,
});

const mapDispatchToProps = (dispatch) => ({
  getUserData: (payload) => dispatch(getUserData(payload)),
  getProjectList: (payload) => dispatch(getProjectList(payload)),
  setSubscriptionJourneyModal: (payload) =>
    dispatch(setSubscriptionJourneyModal(payload)),
  setCollaboratorInfoModalStatus: (status) =>
    dispatch(setCollaboratorInfoModalStatus(status)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(withSubscriptionBanner(Index)));
