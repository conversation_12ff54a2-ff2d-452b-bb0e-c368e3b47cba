import React from 'react';
import PropTypes from 'prop-types';
import Style from '../../styles/startApp.module.scss'

const CollaboratorsModalBody = ({ owner, collaborators }) => (
  <div className="container-fluid p-0">
    <div className="row p-0 align-items-center ml-0 mb-24">
      <div className="col-auto p-0 d-flex align-items-center">
        <img
          src={owner?.profileImage ?? owner?.avatar ?? '/assets/jpg/Placeholder_Avatar_320.jpg'}
          alt="owner"
          height="32"
          width="32"
          className="rounded-circle mr-2"
        />
        <span className={Style.collaboratorName}>{owner.username}</span>
        <span className={Style.ownerBadge}>Owner</span>
      </div>
    </div>
    {collaborators.map((collab, idx) => (
      <div className="row m-0 p-0 align-items-center mb-24" key={collab._id || idx}>
        <div className="col-auto p-0 d-flex align-items-center">
          <img src={collab.profileImage || '/assets/jpg/Placeholder_Avatar_320.jpg'} alt={collab.fullName || collab.email} height="32" width="32" className="rounded-circle mr-2" />
          <span className={Style.collaboratorName}>{collab.fullName || collab.email}</span>
        </div>
      </div>
    ))}
  </div>
);

CollaboratorsModalBody.propTypes = {
  owner: PropTypes.object.isRequired,
  collaborators: PropTypes.array.isRequired,
};

export default CollaboratorsModalBody;
