// CreateProjectCard.js
import React from 'react';
import Style from '../styles/startApp.module.scss';
import Icon from 'sharedComponents/Icon/Icon';
import Badge from 'sharedComponents/IconBadge/badgeIcon';
import PlusIconSvgPath from 'svgpath/PlushButtonSvgPath';

const CreateProjectCard = ({ onCreateProject, canCreateProject }) => (
  <div className={Style.newAppContainer}>
    <div className={`${Style.lockIconContainer} px-3`}>
      <Badge
        text="PRO"
        icon="/assets/svg/lockFeatureIcon.svg"
        bgColor="#00E5D5"
        textColor="#05012d"
        className="cursor-pointer"
        textStyle={{
          fontSize: '12px',
          fontWeight: '400',
          letterSpacing: '1px',
          fontFamily: 'chaney',
        }}
        size="sm"
        onClick={onCreateProject}
        show={!canCreateProject}
      />
    </div>
    <div className={Style.newAppSubContainer}>
      <div className={Style.plusContainer}>
        <div
          data-cy="newProjectContainer"
          className={Style.plusBorder}
          onClick={onCreateProject}
        >
          <Icon icon={PlusIconSvgPath} color="#00000" iconSize="50px" />
        </div>
      </div>
      <div className={Style.existingTitle}>
        <p
          className="p1 text-primary text-center mt-32"
          style={{ cursor: 'pointer' }}
          onClick={onCreateProject}
        >
          Create New Project
        </p>
      </div>
    </div>
  </div>
);

export default CreateProjectCard;
