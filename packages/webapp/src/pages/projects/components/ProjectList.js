// ProjectList.js
import React, { useState } from 'react';
import Style from '../styles/startApp.module.scss';
import ProjectCard from './ProjectCard';
import CreateProjectCard from './CreateProjectCard';
import { useDispatch } from 'react-redux';
import { setSubscriptionJourneyModal } from 'reducer/subscription';

const ProjectList = ({
  projects,
  type,
  accessibleFeature,
  subscriptionStatus,
  collaboratorList,
  canCreateProject,
  isProjectLocked,
  isCollaborationLocked,
}) => {
  const [openMenuIndex, setOpenMenuIndex] = useState(null);
  const dispatch = useDispatch();

  const onCreateProject = () => {
    if (canCreateProject) {
      dispatch(setSubscriptionJourneyModal({
        modalStatus: false,
        feature: '',
        cameFrom: 'projects',
        onComplete: () => { },
      }));
      if (typeof window !== 'undefined') {
        window.location.href = '/project/create';
      }
    } else {
      dispatch(setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'projects',
        onComplete: onCreateProject,
        feature: 'unlimitedProjects',
      }));
    }
  };

  return (
    <div className={Style.gridContainer}>
      {type === 'my' && (
        <CreateProjectCard
          onCreateProject={onCreateProject}
          canCreateProject={canCreateProject}
        />
      )}
      {projects.map((project, index) => (
        <ProjectCard
          isProjectLocked={isProjectLocked(index)}
          key={project._id}
          cover={project.cover}
          item={project}
          index={index}
          accessibleFeature={accessibleFeature}
          subscriptionStatus={subscriptionStatus}
          type={type}
          collaboratorList={collaboratorList}
          isCollaborationLocked={isCollaborationLocked}
          isLocked={type === 'my' ? isProjectLocked(index) : isCollaborationLocked}
          isOpen={openMenuIndex === index}
          onMenuToggle={() => setOpenMenuIndex(openMenuIndex === index ? null : index)}
        />
      ))}
    </div>
  );
};

export default ProjectList;
