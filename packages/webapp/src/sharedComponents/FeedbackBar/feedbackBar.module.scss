.feedbackBar {
  background: #0a0019;
  padding: 16px;
  text-align: center;
  border-radius: 0px 0px 4px 4px;
}

.title {
  color: #fff;
  /* Heading/H6 */
  font-family: CHANEY;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.button {
  flex: 1;
  padding: 8px 0;
  border: 1px solid;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 400;
  transition: all 0.3s ease;
  background: transparent;
  cursor: pointer;
  text-align: center;
  font-family: 'maisonNeue';
  line-height: 150%;
}

.cyan {
  border-color: #00e5d5;
  color: #00e5d5;

  &:hover {
    background: #00e5d5;
    color: #000;
  }
}

.lime {
  border-color: #ebff29;
  color: #ebff29;

  &:hover {
    background: #ebff29;
    color: #000;
  }
}

.red {
  border-color: #ff303d;
  color: #ff303d;
  &:hover {
    background: #ff303d;
    color: #000;
  }
}
