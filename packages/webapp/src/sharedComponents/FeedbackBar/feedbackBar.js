import React from 'react';
import Style from './feedbackBar.module.scss';

const FeedbackBar = ({
  title = 'LEAVE FEEDBACK',
  options = [],
  className = '',
}) => {
  return (
    <div className={`${Style.feedbackBar} ${className}`}>
      <h3 className={`mb-12 ${Style.title}`}>{title}</h3>
      <div className={Style.buttons}>
        {options.map((option, idx) => (
          <button
            key={idx}
            onClick={option.onClick}
            className={`${Style.button} ${Style[option.color]}`}
          >
            {option.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default FeedbackBar;
