/* eslint-disable no-useless-escape */
/* eslint-disable jsx-a11y/interactive-supports-focus */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable eqeqeq */
/* eslint-disable jsx-a11y/no-static-element-interactions */

import React from 'react';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import Router from 'next/router';
import { get } from 'lodash';
import Style from '../pages/projects/styles/startApp.module.scss';

// Project cover data show form component
class Cover extends React.PureComponent {
  handleOpenProject = (item) => {
    const {
      index,
      accessibleFeature,
      setSubscriptionJourneyModal,
      isCollaboratingProject,
    } = this.props;
    if (isCollaboratingProject) {
      // For collaborating projects, lock if not allowed or expired
      if (
        !accessibleFeature.projectCollaborators ||
        this.props.subscriptionStatus === 'expired'
      ) {
        setSubscriptionJourneyModal({
          modalStatus: true,
          cameFrom: 'dashboard',
          onComplete: () => this.handleOpenProject(item),
          feature: 'projectCollaborators',
        });
      } else {
        setSubscriptionJourneyModal({
          modalStatus: false,
          feature: '',
          cameFrom: 'dashboard',
          onComplete: () => {},
        });
        Router.push(
          '/project/overview/[homePage]',
          `/project/overview/${item._id}`,
        );
      }
    } else {
      // For existing projects, use the original logic
      if (
        (index > 1 && !accessibleFeature.unlimitedProjects) ||
        (index > 1 && this.props.subscriptionStatus === 'expired')
      ) {
        setSubscriptionJourneyModal({
          modalStatus: true,
          cameFrom: 'dashboard',
          onComplete: () => this.handleOpenProject(item),
          feature: 'unlimitedProjects',
        });
      } else {
        setSubscriptionJourneyModal({
          modalStatus: false,
          feature: '',
          cameFrom: 'dashboard',
          onComplete: () => {},
        });
        Router.push(
          '/project/overview/[homePage]',
          `/project/overview/${item._id}`,
        );
      }
    }
  };
  render() {
    const { t, cover, item, callout } = this.props;

    return (
      <div
        className={`${Style.container}`}
        onClick={
          callout ? null : () => this.handleOpenProject(item)

          // !callout &&
          // Router.push(
          //   '/project/overview/[homePage]',
          //   `/project/overview/${item._id}`,
          // )
        }
      >
        <div className="row m-0 p-0 justify-content-center">
          <p data-cy="producerTitle" className={`${Style.headers}`}>
            {t('common:projectCreate.preview.producedBy')}
          </p>
          <p
            data-cy="producerName"
            className={`${Style.subHeader}`}
            style={{ maxWidth: '225px' }}
          >
            {get(cover, 'producer') && cover.producer}
          </p>
        </div>
        <div className={`${Style.hr}`} />
        <div className="container">
          <div className="row">
            <div className={`${Style.titleContainer} col-12 text-center`}>
              <p data-cy="titleText" className={`${Style.title}`}>
                {get(cover, 'title') && cover.title}
              </p>
            </div>
          </div>
        </div>
        <div className={`${Style.hr}`} />
        <div className="container">
          <div className="row">
            <div className="col">
              <div className={`${Style.director} row`}>
                <p data-cy="directorTitle" className={`${Style.headers}`}>
                  {t('common:projectCreate.preview.directedBy')}
                </p>

                <p data-cy="directorName" className={`${Style.subHeader}`}>
                  {get(cover, 'director') && cover.director}
                </p>
              </div>
            </div>
            <div className={`${Style.vl}`} />
            <div className="col">
              <div className={`${Style.writer} row`}>
                <p data-cy="writerTitle" className={`${Style.headers}`}>
                  {t('common:projectCreate.preview.writtenBy')}
                </p>

                <p data-cy="writerName" className={`${Style.subHeader}`}>
                  {get(cover, 'writer') && cover.writer}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
}

Cover.defaultProps = { callout: false, isCollaboratingProject: false };

Cover.propTypes = {
  t: PropTypes.func.isRequired,
  cover: PropTypes.object.isRequired,
  item: PropTypes.object.isRequired,
  index: PropTypes.number,
  accessibleFeature: PropTypes.object.isRequired,
  setSubscriptionJourneyModal: PropTypes.func.isRequired,
  subscriptionStatus: PropTypes.string.isRequired,
  callout: PropTypes.bool,
  isCollaboratingProject: PropTypes.bool,
};

export default withTranslation('common')(Cover);
