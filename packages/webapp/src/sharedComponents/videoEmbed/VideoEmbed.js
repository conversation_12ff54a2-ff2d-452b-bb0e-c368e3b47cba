/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import Lightbox from 'sharedComponents/lightBox';
import InlineSvg from 'sharedComponents/inline-svg';

class VideoEmbed extends PureComponent {
  state = { isOpenLightbox: false, slide: 0 };

  toggleLightbox = (index) =>
    this.setState((prev) => ({
      isOpenLightbox: !prev.isOpenLightbox,
      slide: index,
    }));

  getVideoInfo = (url) => {
    const youtube = url?.match(
      /(?:youtube\.com\/(?:[^/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?/\\s]{11})/,
    );
    const vimeo = url?.match(/(?:vimeo\.com\/(?:video\/)?)(\d+)/);
    if (!youtube && !vimeo) return null;
    const isYoutube = !!youtube;
    const id = isYoutube ? youtube[1] : vimeo[1];
    return {
      id,
      thumbnail: isYoutube
        ? `http://img.youtube.com/vi/${id}/hqdefault.jpg`
        : `https://vumbnail.com/${id}.jpg`,
      embedUrl: isYoutube
        ? `https://www.youtube.com/embed/${id}`
        : `https://player.vimeo.com/video/${id}`,
    };
  };

  render() {
    const { videos, customStyles = {}, renderWrapper } = this.props;
    const { isOpenLightbox, slide } = this.state;

    const videoElements = videos.map((video, index) => {
      const info = this.getVideoInfo(video.url);

      return (
        <div
          key={video.url || index}
          className={customStyles.containerClass || 'm0Auto'}
        >
          {!info ? (
            <p className="text-danger text-center">
              Unsupported video URL: {video.url}
            </p>
          ) : (
            <>
              <div className={customStyles.videoClass || ''}>
                <img
                  src={info.thumbnail}
                  alt="videoPic"
                  className={customStyles.imageClass || ''}
                  onClick={() => this.toggleLightbox(index + 1)}
                />
                <i>
                  <InlineSvg
                    src="/assets/svg/playButton.svg"
                    alt="videoPic"
                    width="80px"
                    height="60px"
                    {...(customStyles.playButtonProps || {})}
                    onClick={() => this.toggleLightbox(index + 1)}
                  />
                </i>
              </div>
              {video.title && (
                <div className={customStyles.titleClass || ''}>
                  {video.title}
                </div>
              )}
              {video.disc && (
                <div className={customStyles.descClass || ''}>{video.disc}</div>
              )}
            </>
          )}
        </div>
      );
    });

    const videoUrls = videos
      .map((v) => this.getVideoInfo(v.url))
      .filter(Boolean)
      .map((info) => (
        <iframe
          key={info.id}
          width="760"
          height="400"
          src={info.embedUrl}
          style={{ border: 'none' }}
          allowFullScreen
        />
      ));

    return (
      <>
        {renderWrapper ? (
          renderWrapper(videoElements)
        ) : (
          <div className="pt-4">{videoElements}</div>
        )}
        <Lightbox
          isVisible={isOpenLightbox}
          urls={videoUrls}
          slide={slide}
          type="video"
        />
      </>
    );
  }
}

VideoEmbed.propTypes = {
  videos: PropTypes.array.isRequired,
  customStyles: PropTypes.shape({
    containerClass: PropTypes.string,
    videoClass: PropTypes.string,
    imageClass: PropTypes.string,
    playButtonProps: PropTypes.object,
    titleClass: PropTypes.string,
    descClass: PropTypes.string,
  }),
  renderWrapper: PropTypes.func,
};

VideoEmbed.defaultProps = {
  customStyles: {},
  renderWrapper: null,
};

export default VideoEmbed;
