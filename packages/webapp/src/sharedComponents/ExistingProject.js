/* eslint-disable @next/next/no-img-element */
import React, { PureComponent } from 'react';
import Router from 'next/router';
import PropTypes from 'prop-types';
import { reduxForm } from 'redux-form';
import { get, filter } from 'lodash';
import Modal from 'sharedComponents/Modal/modal';
import { withTranslation } from 'react-i18next';
import Style from '../pages/projects/styles/startApp.module.scss';
import style from '../pages/projects/styles/collaborator.module.scss';
import Collaborators from './Collaborator';
import ProjectMenu from './ProjectMenu';

class ExisitingProject extends PureComponent {
  constructor() {
    super();
    this.state = {
      showEdit: false,
      showModal: false,
      isShowCollaborators: false,
      isShowCollaboratorsDelete: false,
      collaboratorDeleteId: null,
      collaboratorName: null,
      collaboratorEmail: null,
      projectCollaborators: false,
    };
  }

  // This method used for close modal.
  closeModal = () => {
    this.setState({
      showModal: false,
      showEdit: false,
      isShowCollaborators: false,
      isShowCollaboratorsDelete: false,
      collaboratorDeleteId: null,
      collaboratorName: null,
      collaboratorEmail: null,
    });
  };

  // This method used for open modal.
  showModalFunc = () => {
    this.setState({
      showModal: true,
      showEdit: false,
      isShowCollaborators: false,
      isShowCollaboratorsDelete: false,
    });
  };

  // This method used for open collaborator modal.
  showCollaboratorsModal = (item) => {
    if (
      this.props.accessibleFeature.projectCollaborators &&
      this.props.subscriptionStatus !== 'expired'
    ) {
      this.props.setSubscriptionJourneyModal({
        modalStatus: false,
        feature: '',
        onComplete: () => {},
      });
      const { fetchCollaboratorList } = this.props;
      fetchCollaboratorList(item._id);
      this.setState({
        showModal: false,
        showEdit: false,
        isShowCollaborators: true,
        isShowCollaboratorsDelete: false,
      });
    } else {
      this.props.setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'dashboard',
        onComplete: () => {
          this.showCollaboratorsModal(item);
        },
        feature: 'projectCollaborators',
      });
    }
  };

  // This method used to soft delete project.
  deleteSpecificProject = (item) => {
    const { softDeleteProject } = this.props;
    softDeleteProject(item._id);
  };

  submitEmailForm = async (values, _, props) => {
    const { createCollaborator, fetchCollaboratorList } = this.props;
    const name = get(props, 'item.creator.username', '');
    const projectId = get(props, 'item._id');
    const title = get(props, 'item.cover.title');
    const producerName = get(props, 'item.cover.producer');
    const directorName = get(props, 'item.cover.director');
    const writerName = get(props, 'item.cover.writer');
    const creatorId = get(props, 'item.creator.userId');
    if (projectId) {
      const value = {
        projectCreatorInfo: {
          projectCreatorId: creatorId,
          fullName: name,
        },
        projectInfo: {
          projectsId: projectId,
          title: title,
          producer: producerName,
          director: directorName,
          writer: writerName,
        },
        status: 'pending',
        email: values.inviteEmail,
        profileImage: null,
        fullName: null,
        reminders: [
          {
            date: new Date().toJSON().slice(0, 10).replace(/-/g, '/'),
          },
        ],
      };
      await createCollaborator(value);
    }
    await fetchCollaboratorList(projectId);
  };

  // Send reminder to collaborators.
  sendReminder = async (email, projectId) => {
    const { sendReminderToCollaborator, fetchCollaborator } = this.props;
    let collaboratorData = null;
    if (email && projectId) {
      collaboratorData = await fetchCollaborator({
        email: encodeURIComponent(email),
        id: projectId,
      });
      const data = collaboratorData ? collaboratorData[0] : false;
      const id = await get(data, '_id');
      const oldDates = await get(data, 'reminders');
      const reninderNewDate = {
        date: new Date().toJSON().slice(0, 10).replace(/-/g, '/'),
      };
      oldDates.push(reninderNewDate);
      if (data) {
        const value = {
          projectCreatorInfo: {
            projectCreatorId: data.projectCreatorInfo.projectCreatorId,
            fullName: get(data, 'projectCreatorInfo.fullName', ''),
          },
          projectInfo: {
            projectsId: projectId,
            title: data.projectInfo.title,
            producer: data.projectInfo.producer,
            director: data.projectInfo.director,
            writer: data.projectInfo.writer,
          },
          status: 'pending',
          email: data.email,
          profileImage: null,
          fullName: null,
          reminders: oldDates,
        };
        sendReminderToCollaborator(value, id);
      }
    }
  };

  deleteSpecificCollaborator = (item) => {
    const { deleteCollaborator, removeSectionItems } = this.props;
    const { collaboratorDeleteId, collaboratorEmail } = this.state;
    const projectCollaborators = get(item, 'projectCollaborator');
    const isRemoveItem = filter(projectCollaborators, {
      email: collaboratorEmail,
    });

    if (isRemoveItem.length > 0) {
      const removeItemId = isRemoveItem[0]._id;
      removeSectionItems(item._id, 'projectCollaborator', removeItemId);
    }
    this.closeModal();
    deleteCollaborator(collaboratorDeleteId);
  };

  isDeleteCollaborator = (id, name, email) => {
    this.setState({
      showModal: false,
      showEdit: false,
      isShowCollaborators: false,
      isShowCollaboratorsDelete: true,
      collaboratorDeleteId: id,
      collaboratorName: name,
      collaboratorEmail: email,
    });
  };

  handleOpenProject = (item) => {
    const { index, accessibleFeature, setSubscriptionJourneyModal } =
      this.props;
    if (
      (index > 1 && !accessibleFeature.unlimitedProjects) ||
      (index > 1 && this.props.subscriptionStatus === 'expired')
    ) {
      setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'dashboard',
        onComplete: this.handleOpenProject,
        feature: 'unlimitedProjects',
      });
    } else {
      Router.push(
        '/project/overview/[homePage]',
        `/project/overview/${item._id}`,
      );
    }
  };
  handleOpenModal = () => {
    const { index, accessibleFeature, setSubscriptionJourneyModal } =
      this.props;
    if (
      (index > 1 && !accessibleFeature.unlimitedProjects) ||
      (index > 1 && this.props.subscriptionStatus === 'expired')
    ) {
      setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'dashboard',
        onComplete: this.handleOpenModal,
        feature: 'unlimitedProjects',
      });
    } else {
      this.setState({
        showEdit: !this.state.showEdit,
      });
    }
  };

  render() {
    const {
      showModal,
      isShowCollaborators,
      isShowCollaboratorsDelete,
      collaboratorName,
    } = this.state;
    const { item, accessibleFeature, index, openMenuId, setOpenMenuId } =
      this.props;

    return (
      <div>
        <Modal
          modalShow={showModal}
          title="Are you sure you want to delete this project?"
          body="It will be relocated to the Recently Removed Projects section for 30 days before being permanently removed."
          closeCallback={() => this.closeModal()}
          closeBtnText="CANCEL"
          successCallback={() => this.deleteSpecificProject(item)}
          successBtnText="DELETE"
          titleClass={`${Style.modalTitle} m-0`}
          modalSize="custom"
          closeBtnClass="cancelButton"
        />
        <Modal
          modalShow={isShowCollaborators}
          title="Collaborators"
          body={
            <Collaborators
              projectId={item._id}
              collaboratorList={this.props.collaboratorList}
              handleSubmit={this.props.handleSubmit}
              submitEmailForm={this.submitEmailForm}
              sendReminder={this.sendReminder}
              isDeleteCollaborator={this.isDeleteCollaborator}
              t={this.props.t}
            />
          }
          closeCallback={() => this.closeModal()}
          titleClass={`${Style.modalTitle}`}
          modalSize="xl"
          className={style.inviteModal}
          modalHeader={style.modalHeader}
          svgIconClass={style.svgIconClass}
          modalFooter={style.modalFooter}
        />
        <Modal
          modalShow={isShowCollaboratorsDelete}
          title="Remove collaborator?"
          body={`Are you sure you want to remove ${collaboratorName} as a collaborator? They won’t be able to access this project anymore.`}
          closeCallback={() => this.closeModal()}
          closeBtnText="CANCEL"
          successCallback={() => this.deleteSpecificCollaborator(item)}
          successBtnText="DELETE"
          titleClass={`${Style.modalTitle}`}
          closeBtnClass="cancelButton"
          className={style.inviteModal}
          svgIconClass={style.svgIconClass}
          modalSize="xl"
          modalFooter={style.footerRightAlign}
          bodyClass={style.bodyClass}
        />
        <ProjectMenu
          item={item}
          index={index}
          accessibleFeature={accessibleFeature}
          subscriptionStatus={this.props.subscriptionStatus}
          setSubscriptionJourneyModal={this.props.setSubscriptionJourneyModal}
          onEdit={() => this.handleOpenProject(item)}
          onCollaborators={() => this.showCollaboratorsModal(item)}
          onDelete={() => this.showModalFunc(item)}
          showCollaboratorsOption={true}
          showDeleteOption={true}
          showCollaboratorsLock={
            !accessibleFeature.projectCollaborators ||
            this.props.subscriptionStatus === 'expired'
          }
          isOpen={openMenuId === item._id}
          onMenuToggle={() =>
            setOpenMenuId(openMenuId === item._id ? null : item._id)
          }
        />
      </div>
    );
  }
}
ExisitingProject.defaultProps = {};

ExisitingProject.propTypes = {
  item: PropTypes.func.isRequired,
  deleteProject: PropTypes.func.isRequired,
  collaboratorList: PropTypes.array.isRequired,
  handleSubmit: PropTypes.func.isRequired,
  t: PropTypes.object.isRequired,
  deleteCollaborator: PropTypes.func.isRequired,
  fetchCollaborator: PropTypes.func.isRequired,
  sendReminderToCollaborator: PropTypes.func.isRequired,
  fetchCollaboratorList: PropTypes.func.isRequired,
  createCollaborator: PropTypes.func.isRequired,
  userId: PropTypes.string.isRequired,
  removeSectionItems: PropTypes.func.isRequired,
};

export default reduxForm({
  form: 'inviteForm',
})(withTranslation('common')(ExisitingProject));
