import React from 'react';
import PropTypes from 'prop-types';
import InlineSvg from 'sharedComponents/inline-svg';
import classNames from 'classnames';

const sizePresets = {
  sm: {
    padding: '1px 4px',
    iconSize: 12,
    fontSize: '10px',
  },
  md: {
    padding: '2px 6px',
    iconSize: 14,
    fontSize: '14px',
  },
  lg: {
    padding: '4px 8px',
    iconSize: 18,
    fontSize: '18px',
  },
};

const Badge = ({
  text,
  icon,
  bgColor,
  textColor,
  borderRadius,
  className,
  onClick,
  textStyle,
  size,
  show,
}) => {
  const badgeClasses = classNames(
    'd-inline-flex align-items-center justify-content-center',
    className,
  );

  const { padding, iconSize, fontSize } = sizePresets[size] || sizePresets.md;

  if (!show) {
    return null;
  }

  return (
    <div
      className={badgeClasses}
      onClick={onClick}
      style={{
        backgroundColor: bgColor,
        color: textColor,
        padding,
        borderRadius: borderRadius ? '4px' : '0',
        cursor: onClick ? 'pointer' : 'default',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        columnGap: text && icon ? '3px' : '0',
      }}
    >
      {icon && (
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <InlineSvg src={icon} height={iconSize} width={iconSize} />
        </span>
      )}
      {text && (
        <span
          style={{
            display: 'flex',
            alignItems: 'center',
            fontSize,
            fontWeight: 600,
            textTransform: 'uppercase',
            ...textStyle,
          }}
        >
          {text}
        </span>
      )}
    </div>
  );
};

Badge.propTypes = {
  text: PropTypes.string,
  icon: PropTypes.string,
  bgColor: PropTypes.string,
  textColor: PropTypes.string,
  borderRadius: PropTypes.bool,
  className: PropTypes.string,
  onClick: PropTypes.func,
  textStyle: PropTypes.object,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
};

Badge.defaultProps = {
  text: '',
  icon: '',
  bgColor: 'transparent',
  textColor: 'inherit',
  borderRadius: false,
  className: '',
  onClick: null,
  textStyle: {
    fontWeight: 500,
    lineHeight: '1',
    textTransform: 'uppercase',
    letterSpacing: '0.5px',
  },
  size: 'md',
};

export default Badge;
