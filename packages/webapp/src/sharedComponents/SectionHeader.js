import React from 'react';
import PropTypes from 'prop-types';

const SectionHeader = ({ title, description }) => (
  <div className="text-center">
    <div className="row align-items-center">
      <div className="col-md-4 d-none d-md-block"></div>
      <div className="col-12 col-md-4 text-center">
        <h1 className="text-primary mb-3 mb-md-0 fs-20 fs-md-24 fs-lg-24">
          {title}
        </h1>
      </div>
    </div>
    {description && (
      <div className="d-none d-sm-none d-md-block">
        <p className="text-primary text-center p1 mt-16 mb-0 mb-md-34 mb-lg-34">
          {description}
        </p>
      </div>
    )}
  </div>
);

SectionHeader.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string,
};

export default SectionHeader;
