const { isEmpty } = require('lodash');
const Submission = require('../models/Submission');
const Project = require('../models/project');
const Callout = require('../models/callout');
const ProjectSnap = require('../models/projectSnaps');

exports.create = async ({ calloutId, snapshotId, user }) => {
  // Only fetch required fields from snapshot
  const snapshot =
    await ProjectSnap.findById(snapshotId).select('projectId creator');
  if (!snapshot) throw new Error('Snapshot not found');
  // Only fetch required fields from project
  const project = await Project.findById(snapshot.projectId).select('creator');
  if (!project) throw new Error('Project not found');
  // Only fetch required fields from callout
  const callout = await Callout.findById(calloutId).select('_id');
  if (!callout) throw new Error('Callout not found');

  const submission = await Submission.create({
    calloutId,
    projectId: project._id,
    snapshotId,
    projectCreator: project.creator,
    snapshotCreator: snapshot.creator,
    submittedBy: {
      userId: user._id,
      email: user.email,
      fullName: user.fullName,
      profileImage: user.profileImage,
    },
  });

  await Project.findByIdAndUpdate(project._id, {
    $inc: { totalSubmissions: 1 },
  });
  await Callout.findByIdAndUpdate(callout._id, {
    $inc: { totalSubmissions: 1 },
  });

  return submission;
};

exports.updateSubmissionType = async (
  submissionId,
  user,
  toType = 'slate',
  status = 'NEW',
  isEmailSent = false,
) => {
  const submission = await Submission.findById(submissionId);
  if (!submission) throw new Error('Submission not found');
  const calloutId = submission.calloutId;

  // Update submission type
  const updated = await Submission.findByIdAndUpdate(
    submissionId,
    {
      type: toType,
      slateUpdatedBy: user,
      slateUpdatedAt: new Date(),
      status,
      isEmailSent,
      ...(toType === 'slate'
        ? {
            activity: {
              type: 'slate',
              action: 'not_view',
            },
          }
        : {
            // Clear activity
            activity: null,
          }),
    },
    { new: true },
  );

  if (toType === 'slate') {
    // Move from submissions to slates
    await exports.updateCalloutArrayAndCount(
      calloutId,
      'submissions',
      submissionId,
      'remove',
    );
    await exports.updateCalloutArrayAndCount(
      calloutId,
      'slates',
      submissionId,
      'add',
    );
  } else {
    // Move from slates to submissions
    await exports.updateCalloutArrayAndCount(
      calloutId,
      'slates',
      submissionId,
      'remove',
    );
    await exports.updateCalloutArrayAndCount(
      calloutId,
      'submissions',
      submissionId,
      'add',
    );
  }

  return updated;
};

exports.addFeedback = async (submissionId, feedback, user) => {
  // Fetch the current type of the submission
  const submission = await Submission.findById(submissionId).select('type');
  if (!submission) throw new Error('Submission not found');
  return Submission.findByIdAndUpdate(
    submissionId,
    {
      $push: { feedback: { feedback, addedBy: user, entity: submission.type } },
    },
    { new: true },
  );
};

exports.addNote = async (submissionId, note) => {
  return Submission.findByIdAndUpdate(
    submissionId,
    { $set: { note } },
    { new: true },
  );
};

// Helper to validate status based on type
function isValidStatusForType(type, status) {
  if (type === 'submission') {
    return ['NEW', 'REJECTED', 'FEEDBACK_SENT'].includes(status);
  } else if (type === 'slate') {
    return [
      'AWAITING_FEEDBACK',
      'LETS_TALK',
      'NOT_INTERESTED',
      'TRACKING',
    ].includes(status);
  }
  return false;
}
exports.isValidStatusForType = isValidStatusForType;

exports.getDetail = async (submissionId) => {
  return Submission.findById(submissionId)
    .populate({ path: 'snapshotId', select: 'name' })
    .populate({ path: 'projectId', select: 'name' })
    .populate({ path: 'calloutId', select: 'name' })
    .lean();
};

/**
 * Track submission activity
 * @param {string} submissionId - Submission ID
 * @param {string} entity - Entity type ('submission' | 'slate')
 * @param {string} action - Action performed ('view' | 'action2' | 'action3')
 * @returns {Object} Updated submission
 */
// ...existing code...
exports.markActivity = async (submissionId, entity, action) => {
  const submission = await Submission.findById(submissionId);
  if (!submission) throw new Error('Submission not found');

  // Only update if not already viewed
  if (
    submission.activity &&
    submission.activity.entity === entity &&
    submission.activity.action === 'view'
  ) {
    return submission;
  }

  submission.activity = {
    entity,
    action,
    addedAt: new Date(),
  };
  await submission.save();

  return submission;
};
// ...existing code...

exports.fetch = async (where = {}, options = {}) => {
  try {
    // If options.populate is already set (from controller), use it directly
    if (options.populate) {
      return Submission.paginate(where, options);
    }

    // Support dynamic $select for project, callout, snapshot fields
    const selectParam = options.select || '';
    if (!selectParam.trim()) {
      // If $select is missing or empty, do not populate
      return Submission.paginate(where, options);
    }
    const selectFields = selectParam
      .split(',')
      .map((f) => f.trim())
      .filter(Boolean);
    const rootSelect = [];
    const populateFields = { projectId: [], calloutId: [], snapshotId: [] };
    const snapshotBodyFields = [];

    selectFields.forEach((field) => {
      if (field.startsWith('project.')) {
        populateFields.projectId.push(field.replace('project.', ''));
      } else if (field.startsWith('callout.')) {
        populateFields.calloutId.push(field.replace('callout.', ''));
      } else if (field.startsWith('snapshot.body.')) {
        // e.g. snapshot.body.title
        snapshotBodyFields.push(field.replace('snapshot.body.', ''));
        // Ensure we fetch snapshot.body
        if (!populateFields.snapshotId.includes('body')) {
          populateFields.snapshotId.push('body');
        }
      } else if (field.startsWith('snapshot.')) {
        populateFields.snapshotId.push(field.replace('snapshot.', ''));
      } else if (field) {
        rootSelect.push(field);
      }
    });

    const mongoosePopulate = [];
    Object.entries(populateFields).forEach(([path, fields]) => {
      if (fields.length) {
        mongoosePopulate.push({ path, select: fields.join(' ') });
      }
    });

    const paginateOptions = {
      ...options,
      select: rootSelect.length ? rootSelect.join(' ') : undefined,
      populate: mongoosePopulate.length ? mongoosePopulate : undefined,
    };
    // Run the query
    const result = await Submission.paginate(where, paginateOptions);
    if (result && Array.isArray(result.docs)) {
      result.docs = result.docs.map((doc) => {
        // Process snapshot body fields if needed
        // If snapshot.body.* fields were requested, parse and inject them into snapshot object
        if (snapshotBodyFields.length && doc.snapshotId) {
          if (typeof doc.snapshotId.body === 'string') {
            extractAndAssignNestedFields(
              doc.snapshotId,
              doc.snapshotId.body,
              snapshotBodyFields,
            );
          } else {
            // If snapshot exists but no body, still add nulls for requested fields
            extractAndAssignNestedFields(
              doc.snapshotId,
              '',
              snapshotBodyFields,
            );
          }
        }
        // Destructure and rename fields
        const { projectId, calloutId, snapshotId, ...rest } = doc;
        return {
          ...rest,
          project: projectId,
          callout: calloutId,
          snapshot: snapshotId,
        };
      });
    }
    return result;
  } catch (error) {
    console.error('Error in SubmissionService.pagination:', error);
    throw new Error('Failed to paginate submissions');
  }
};

/**
 * Update callout's submissions/slates array and counters
 * @param {String} calloutId
 * @param {'submissions'|'slates'} field - which array to update
 * @param {String} id - submission/slate id
 * @param {'add'|'remove'} action
 */
exports.updateCalloutArrayAndCount = async (calloutId, field, id, action) => {
  if (!['submissions', 'slates'].includes(field))
    throw new Error('Invalid field');
  if (!['add', 'remove'].includes(action)) throw new Error('Invalid action');
  const countField =
    field === 'submissions' ? 'totalSubmissions' : 'totalSlates';

  const update = {};
  if (action === 'add') {
    update.$addToSet = { [field]: id };
    update.$inc = { [countField]: 1 };
  } else {
    update.$pull = { [field]: id };
    update.$inc = { [countField]: -1 };
  }

  return Callout.findByIdAndUpdate(calloutId, update, { new: true });
};

/**
 * Update callouts in bulk with specific allowed fields only
 * @param {Array<String>} ids - Array of callout IDs to update
 * @param {Object} data - Object with field-value pairs to update, format: {field: value}
 * @description Only allows updating specific fields like 'status' and 'isEmailSent' for security
 */
exports.bulkUpdate = async (ids, data) => {
  if (isEmpty(ids)) {
    throw new Error(
      'Invalid condition - bulk update callout: ids array cannot be empty',
    );
  }

  // Define allowed fields for bulk update
  const allowedFields = ['status', 'isEmailSent'];

  // Validate data parameter
  if (!data || typeof data !== 'object') {
    throw new Error(
      'Invalid data format - expected object with field-value pairs',
    );
  }

  // Check if any field in data is not allowed
  const invalidFields = Object.keys(data).filter(
    (field) => !allowedFields.includes(field),
  );
  if (invalidFields.length > 0) {
    throw new Error(
      `Invalid fields for bulk update: ${invalidFields.join(
        ', ',
      )}. Allowed fields: ${allowedFields.join(', ')}`,
    );
  }
  return Submission.updateMany({ _id: { $in: ids } }, { $set: data });
};

/**
 * TEMPORARY: Utility to extract nested fields from a JSON string and assign them to a target object.
 *
 * NOTE: This is a workaround because snapshot.body is currently stored as a string in the DB.
 * In the future, snapshot.body should be stored as an object, and this function should be removed.
 *
 * @param {Object} target - The object to assign fields to (e.g., doc.snapshotId)
 * @param {String} jsonString - The JSON string to parse (e.g., doc.snapshotId.body)
 * @param {Array<String>} fields - Array of dot-notated field paths (e.g., ['cover.coverPic', 'title'])
 */
function extractAndAssignNestedFields(target, jsonString, fields) {
  let parsedBody = {};
  if (typeof jsonString === 'string') {
    try {
      parsedBody = JSON.parse(jsonString);
    } catch (e) {
      parsedBody = {};
      // Optionally log error for debugging
      // console.error('Failed to parse snapshot body JSON:', e);
    }
  }
  fields.forEach((field) => {
    const path = field.split('.');
    let value = parsedBody;
    try {
      for (let i = 0; i < path.length; i++) {
        if (value && typeof value === 'object' && path[i] in value) {
          value = value[path[i]];
        } else {
          value = null;
          break;
        }
      }
    } catch (e) {
      value = null;
      // Optionally log error for debugging
      // console.error('Error extracting nested field', field, e);
    }
    // Set the value in target at the correct nested path
    try {
      let assignTarget = target;
      for (let i = 0; i < path.length - 1; i++) {
        if (
          !assignTarget[path[i]] ||
          typeof assignTarget[path[i]] !== 'object'
        ) {
          assignTarget[path[i]] = {};
        }
        assignTarget = assignTarget[path[i]];
      }
      assignTarget[path[path.length - 1]] = value;
    } catch (e) {
      // Optionally log error for debugging
      // console.error('Error assigning nested field', field, e);
    }
  });
  // Remove the raw body field from target
  try {
    delete target.body;
  } catch (e) {
    // Optionally log error for debugging
    // console.error('Error deleting body field from target', e);
  }
}
