{"submissionReceived": {"id": "{{submissionSubmitEmail}}", "subject": "We’ve Received Your SMASH Call Out Submission", "message_data": {"viewCallout": "{{webBaseUrl}}/callouts", "calloutName": "{{callout<PERSON>ame}}", "snapshotImage": "{{snapshotImage}}", "title": "{{title}}", "username": "{{username}}", "logLine": "{{logLine}}", "companyName": "{{companyName}}"}}, "rejectSubmissionEmail": {"id": "{{rejectSubmissionEmail}}", "subject": "Update on your SMASH Call-out submission", "message_data": {"creatorName": "{{<PERSON><PERSON><PERSON>}}", "calloutName": "{{callout<PERSON>ame}}", "calloutUrl": "{{webBaseUrl}}/callouts"}}, "slateStatusChange": {"id": "{{slateStatusChangeTemplateId}}", "subject": "Status Update on SMASH Call-out submission", "message_data": {"viewCallout": "{{webBaseUrl}}/callouts/{{calloutId}}", "calloutName": "{{callout<PERSON>ame}}", "slateStatus": "{{slateStatus}}", "link": "{{opsUrl}}/#/callouts/{{calloutId}}/show", "viewSnapshot": "{{webBaseUrl}}/project/snap/{{snapshotHash}}", "snapshotImage": "{{snapshotImage}}", "title": "{{title}}", "username": "{{username}}", "logLine": "{{logLine}}"}}, "submissionFeedback": {"id": "{{submissionFeedbackTemplateId}}", "subject": "Feedback on your {{type}}", "message_data": {"email": "{{email}}", "calloutName": "{{callout<PERSON>ame}}", "feedback": "{{feedback}}", "viewCallout": "{{webBaseUrl}}/callouts/{{calloutId}}", "viewSnapshot": "{{webBaseUrl}}/project/snap/{{snapshotHash}}", "snapshotImage": "{{snapshotImage}}", "title": "{{title}}", "username": "{{username}}", "logLine": "{{logLine}}"}}, "submissionInSlate": {"id": "{{slateTemplateId}}", "subject": "New submissions to your SMASH Call-out slate", "message_data": {"link": "{{webBaseUrl}}/callouts/{{calloutId}}/slates", "email": "{{email}}", "discovererName": "{{username}}", "projects": "{{projects}}"}}}