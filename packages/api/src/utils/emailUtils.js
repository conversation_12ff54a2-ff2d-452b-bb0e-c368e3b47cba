// Helper function - keep internal
const templates = require('./emailTemplates.json');

function replacePlaceholders(str, variables, templateIds) {
  return str.replace(/{{(.*?)}}/g, (_, key) => {
    key = key.trim();
    // Priority: templateIds (for template IDs like submissionSubmitEmail) > variables (for content)
    if (templateIds && templateIds[key]) return templateIds[key];

    if (variables[key]) {
      if (key === 'projects' && typeof variables[key] === 'object') {
        return JSON.stringify(variables[key]);
      }
      return variables[key];
    }

    return ''; // Don't fall back to process.env
  });
}

// Main function
function composeEmail(templateKey, variables = {}) {
  const template = templates[templateKey];
  if (!template) throw new Error(`Email template ${templateKey} not found`);

  // Parse EMAIL_TEMPLATE_DATA from env to get template IDs only
  let templateIds = {};
  if (process.env.EMAIL_TEMPLATE_DATA) {
    try {
      templateIds = JSON.parse(
        process.env.EMAIL_TEMPLATE_DATA.replace(/\\/g, ''),
      );
    } catch (e) {
      templateIds = {};
    }
  }
  const result = {};
  for (const key in template) {
    if (typeof template[key] === 'string') {
      result[key] = replacePlaceholders(template[key], variables, templateIds);
    } else if (typeof template[key] === 'object') {
      result[key] = {};
      for (const subKey in template[key]) {
        result[key][subKey] = replacePlaceholders(
          template[key][subKey],
          variables,
          templateIds,
        );
      }
    }
  }
  return result;
}

// Export for CommonJS
module.exports = { composeEmail };
