const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const server = require('../../src/server');
const Callout = require('../../src/models/callout');

describe('Callout API', () => {
  let mongoServer;
  let app;

  beforeAll(async () => {
    // Start in-memory MongoDB instance
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    // Connect to the in-memory database
    await mongoose.connect(mongoUri);
    
    // Initialize the server
    app = await server.init();
  });

  afterAll(async () => {
    // Clean up
    await mongoose.disconnect();
    await mongoServer.stop();
    await app.stop();
  });

  beforeEach(async () => {
    // Clear the database before each test
    await Callout.deleteMany({});
  });

  describe('POST /v1/callout', () => {
    it('should create a callout with requiredFields', async () => {
      const calloutData = {
        name: 'Test Callout',
        body: {
          companyName: 'Test Company',
          companyProfile: 'Test profile',
          content: 'Test content',
          logo: 'test-logo.png',
          title: 'Test Title',
          companyProfileHeading: 'About Test Company',
        },
        genres: 'Action',
        opportunities: 'Film Production',
        budget: 100000,
        city: {
          address: 'Test City',
          geoCode: { lat: 0, lng: 0 },
        },
        requiredFields: {
          cover: {
            allFields: true,
            fields: {
              coverImage: true,
              title: true,
              producer: false,
              director: false,
              writer: false,
            },
          },
          basicInformation: {
            allFields: false,
            fields: {
              logline: true,
              tags: true,
              genre: false,
              setting: false,
              runtime: false,
              status: false,
              projectHighlights: false,
            },
          },
          description: {
            allFields: false,
            fields: {
              synopsis: true,
              creativeVision: false,
              treatmentBible: false,
              script: false,
            },
          },
          creativeTeam: true,
          castMembers: false,
          moodboard: false,
          projectVideos: false,
          financePlan: false,
          budget: false,
          otherDocuments: false,
          salesEstimate: false,
        },
      };

      const response = await request(app.listener)
        .post('/v1/callout')
        .send(calloutData)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.message).toBe('Callout created successfully');
      expect(response.body.data).toBeDefined();
      expect(response.body.data.requiredFields).toBeDefined();
      expect(response.body.data.requiredFields.cover.allFields).toBe(true);
      expect(response.body.data.requiredFields.cover.fields.coverImage).toBe(true);
      expect(response.body.data.requiredFields.basicInformation.fields.logline).toBe(true);
      expect(response.body.data.requiredFields.creativeTeam).toBe(true);
    });

    it('should create a callout with default requiredFields when not provided', async () => {
      const calloutData = {
        name: 'Test Callout Without Required Fields',
        body: {
          companyName: 'Test Company',
          companyProfile: 'Test profile',
        },
        city: {
          address: 'Test City',
          geoCode: { lat: 0, lng: 0 },
        },
      };

      const response = await request(app.listener)
        .post('/v1/callout')
        .send(calloutData)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.data.requiredFields).toBeDefined();
      // Check that default values are false
      expect(response.body.data.requiredFields.cover.allFields).toBe(false);
      expect(response.body.data.requiredFields.cover.fields.coverImage).toBe(false);
      expect(response.body.data.requiredFields.creativeTeam).toBe(false);
    });
  });

  describe('PATCH /v1/callout/:id', () => {
    it('should update a callout with requiredFields', async () => {
      // First create a callout
      const callout = new Callout({
        name: 'Original Callout',
        body: {
          companyName: 'Original Company',
        },
        city: {
          address: 'Original City',
          geoCode: { lat: 0, lng: 0 },
        },
      });
      await callout.save();

      const updateData = {
        name: 'Updated Callout',
        requiredFields: {
          cover: {
            allFields: false,
            fields: {
              coverImage: true,
              title: true,
              producer: true,
              director: false,
              writer: false,
            },
          },
          basicInformation: {
            allFields: true,
            fields: {
              logline: true,
              tags: true,
              genre: true,
              setting: true,
              runtime: true,
              status: true,
              projectHighlights: true,
            },
          },
          creativeTeam: true,
          moodboard: true,
        },
      };

      const response = await request(app.listener)
        .patch(`/v1/callout/${callout._id}`)
        .send(updateData)
        .expect(200);

      expect(response.body.statusCode).toBe(200);
      expect(response.body.message).toBe('Callout updated successfully');
      expect(response.body.data.name).toBe('Updated Callout');
      expect(response.body.data.requiredFields.cover.fields.producer).toBe(true);
      expect(response.body.data.requiredFields.basicInformation.allFields).toBe(true);
      expect(response.body.data.requiredFields.creativeTeam).toBe(true);
      expect(response.body.data.requiredFields.moodboard).toBe(true);
    });
  });
});
